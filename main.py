import datetime
import logging
import os
import re
import uuid
from logging.config import dictConfig
from typing import List, Literal

import redis
from MailChecker import <PERSON><PERSON><PERSON><PERSON>
from bson import ObjectId
# from dotenv import load_dotenv
from fastapi import FastAPI
from pymongo import MongoClient
from pymongo.collection import Collection
from starlette.responses import JSONResponse
from unidecode import unidecode

import config
from utils import verify_email, find_email
from api_responses import BulkVerifyRequestBody, BulkFindRequestBody
from worker import (bulk_verify_task, bulk_find_task,
                    store_single_task_result_in_db_and_retry_if_greylist)

# ------------------------- SETUP SECTION -------------------------

# load_dotenv()

# Set up console logs using python logging module.
dictConfig(config.LogConfig().dict())
logger = logging.getLogger("email_find_and_verify")

# Redis - For greylist tracking.
redis_host: str = os.environ['FINDVERIFY_REDIS_HOST']
redis_port: str = os.environ['FINDVERIFY_REDIS_PORT']
redis_connection_url: str = f"redis://{redis_host}:{redis_port}/1"
redis_pool = redis.ConnectionPool.from_url(redis_connection_url)
redis_instance = redis.Redis(connection_pool=redis_pool, socket_connect_timeout=120)

# MongoDB for storing results.
mongo_host: str = os.environ['FINDVERIFY_MONGO_HOST']
mongo_port: int = int(os.environ['FINDVERIFY_MONGO_PORT'])
mongo_client = MongoClient(mongo_host, mongo_port)
mongo_db = mongo_client["findverify_db"]
bulk_verify_tasks_collection: Collection = mongo_db["bulk_verify_tasks"]
bulk_find_tasks_collection: Collection = mongo_db["bulk_find_tasks"]

# Fetch the emails to use in verifier script.
verifier_emails: List[str] = os.environ["FINDVERIFY_VERIFIER_EMAILS"].split(",")

# Initialize FastAPI.
app = FastAPI()


# ------------------------- DEFINE APIs BELOW THIS LINE -------------------------

@app.get("/verify-email")
async def single_verify(email_id: str):
    """
    verifies provided email id.

    :return: Python dictionary with following keys
        "address": ["email_id", "domain"]
        "valid_format": bool
        "deliverable": bool
        "full_inbox": bool
        "host_exists": bool
        "catch_all": bool
        "disposable": bool
        "connection_error": bool
        "message": "Status Message"
        "debug_message": "Some message"
    """
    # Remove unicode
    email_id = unidecode(email_id).lstrip().rstrip()

    # Check if it is in valid format
    if not MailChecker.is_valid_email_format(email_id):
        return JSONResponse(status_code=400, content={"message": "Given email id is invalid"})

    # Initialize MailVerifier class and obtain the smtp_instance
    try:
        _, result = verify_email(email_id, verifier_emails, redis_instance)
    except Exception as err:
        logger.critical(err, exc_info=True)
        return JSONResponse(status_code=500, content={"message": "Server Error."})

    # Store result in MongoDB and retry if greylist detected
    store_single_task_result_in_db_and_retry_if_greylist.delay(result, verifier_emails,
                                                               redis_connection_url, "verify")

    return result


@app.get("/find-email")
async def single_find(fname: str, lname: str, domain: str):
    """
    Finds email from given first name, last name and domain values.

    :return: Python dictionary with following keys
        "address": ["email_id", "domain"]
        "user_details": ["fistname", "lastname", "domain"]
        "valid_format": bool
        "deliverable": bool
        "full_inbox": bool
        "host_exists": bool
        "catch_all": bool
        "disposable": bool
        "connection_error": bool
        "message": "Status Message"
        "debug_message": "Some message"
    """
    # Convert domain to required format.
    domain: str = re.sub(r"^(www.|https?://www.|https?://)", "", domain, re.IGNORECASE).rstrip('/')

    # Check if all values are available.
    # We need both or one of firstname & lastname, and domain, to find emails.
    if not ((fname or lname) and domain):
        return JSONResponse(status_code=400, content={"message": "Bad Request. Missing required values."})

    # Run find email.
    try:
        _, result = find_email(fname, lname, domain, verifier_emails, redis_instance)
    except Exception as err:
        logger.critical(err, exc_info=True)
        return JSONResponse(status_code=500, content={"message": "Server Error."})

    # Store result in MongoDB and retry if greylist detected
    store_single_task_result_in_db_and_retry_if_greylist.delay(result, verifier_emails,
                                                               redis_connection_url, "find")

    return result


@app.post("/bulk-verify-emails")
async def bulk_verify(data: BulkVerifyRequestBody):
    """
    Creates bulk verify email task in celery.

    :param data: Should contain "email" key with emails ids as list of string.
    """
    # Generate Task UID.
    task_uid: str = str(uuid.uuid4())

    document = bulk_verify_tasks_collection.insert_one({
        "task_uid": task_uid,
        "created_on": datetime.datetime.now(datetime.timezone.utc),
        "status": "processing",
        "webhook_url": data.webhook_url,
        "results": []
    })

    try:
        # Start Celery task in background for bulk verify.
        bulk_verify_task.delay(
            data.emails,
            verifier_emails,
            redis_connection_url,
            task_uid,
            data.webhook_url,
        )
    except Exception as err:
        # We need to delete the created document in case of error.
        logger.critical(err, exc_info=True)
        bulk_verify_tasks_collection.delete_one({"_id": ObjectId(document.inserted_id)})

    return {"task_uid": task_uid}


@app.post("/bulk-find-emails")
async def bulk_find(data: BulkFindRequestBody):
    """
    Creates bulk find email task in celery.

    :param data: Should contain "userdetails" key with a list of all ["firstname", "lastname", "domain"] lists.
    """
    # Generate Task UID.
    task_uid: str = str(uuid.uuid4())

    # Create the task document in mongodb.
    document = bulk_find_tasks_collection.insert_one({
        "task_uid": task_uid,
        "created_on": datetime.datetime.now(datetime.timezone.utc),
        "status": "processing",
        "results": []
    })

    try:
        # Start Celery task in background for bulk verify.
        bulk_find_task.delay(
            data.userdetails,
            verifier_emails,
            redis_connection_url,
            task_uid,
        )
    except Exception as err:
        # We need to delete the created document in case of error.
        logger.critical(err, exc_info=True)
        bulk_find_tasks_collection.delete_one({"_id": ObjectId(document.inserted_id)})

    return {"task_uid": task_uid}


@app.get("/get-results")
def get_results(task_type: Literal["find", "verify"], task_uid: str):
    """
    Returns task data for given uid.
    :param task_type: "find" or  "verify".
    :param task_uid: Task UID.
    """
    if task_type == "verify":
        document = bulk_verify_tasks_collection.find_one({"task_uid": task_uid})
        if document is None:
            return JSONResponse(status_code=404, content={"message": "Bulk Verify task with this UID was not found."})
        else:
            return {
                "task_uid": task_uid,
                "created_on": document["created_on"],
                "status": document["status"],
                "results": document["results"]
            }

    elif task_type == "find":
        document = bulk_find_tasks_collection.find_one({"task_uid": task_uid})
        if document is None:
            return JSONResponse(status_code=404, content={"message": "Bulk Find task with this UID was not found."})
        else:
            return {
                "task_uid": task_uid,
                "created_on": document["created_on"],
                "status": document["status"],
                "results": document["results"]
            }

    else:
        return JSONResponse(status_code=400, content={"message": "Invalid task type"})
