import concurrent.futures
import logging
import os
from logging.config import dictConfig
from typing import List, Dict, <PERSON>ple

import redis
import requests
from celery import Celery
from pymongo import MongoClient
from pymongo.collection import Collection
from retry import retry

import utils
import config

# ------------------------- SETUP SECTION -------------------------


dictConfig(config.LogConfig().dict())
logger = logging.getLogger("celery")

redis_host: str = os.environ['FINDVERIFY_REDIS_HOST']
redis_port: str = os.environ['FINDVERIFY_REDIS_PORT']
redis_celery_url: str = f"redis://{redis_host}:{redis_port}"

mongo_host: str = os.environ['FINDVERIFY_MONGO_HOST']
mongo_port: int = int(os.environ['FINDVERIFY_MONGO_PORT'])

celery = Celery(__name__)
celery.conf.broker_url = os.environ.get(
    "CELERY_BROKER_URL",
    redis_celery_url
)
celery.conf.result_backend = os.environ.get(
    "CELERY_RESULT_BACKEND",
    redis_celery_url
)


# noinspection PyIncorrectDocstring
@celery.task(bind=True, default_retry_delay=60, max_retries=5, retry_backoff=True)
def store_single_task_result_in_db_and_retry_if_greylist(self,
                                                         result: Dict,
                                                         verifier_emails: List[str],
                                                         redis_connection_url: str,
                                                         task_name: str):
    """
    Stores single find and verify task result in MongoDB and retry if greylist detected

    :param result: Verification result dictionary
    :param verifier_emails: List of email ids to use as FROM address during verification.
    :param redis_connection_url: Redis connection url.
    :param task_name: Name of the task (verify or find)
    """
    logger.info(f"[*] Storing result in MongoDB...")

    # Connect to Redis.
    try:
        redis_pool = redis.ConnectionPool.from_url(redis_connection_url)
        redis_instance = redis.Redis(connection_pool=redis_pool, socket_connect_timeout=120)
    except Exception as err:
        logger.error("Single Task: Could not connect to Redis instance.")
        raise self.retry(exc=err)

    if result["greylist"]:
        logger.info(f"[*] Greylist detected, retrying...")
        result = utils.retry_greylist_domain_with_delay(result, verifier_emails, redis_instance, task_name)

    # Save or update in MongoDB
    utils.save_single_find_or_verify_result(result, task_name)


# noinspection PyIncorrectDocstring
@celery.task(bind=True, default_retry_delay=60, max_retries=5, retry_backoff=True)
def bulk_verify_task(self, email_list: List[str], from_emails_list: List[str], redis_connection_url: str,
                     task_uid: str, webhook_url: str | None):
    """
    Bulk verifies given list of email addresses.

    :param email_list: List of email ids.
    :param from_emails_list: List of email ids for use as FROM address during email verification.
    :param redis_connection_url: Redis connection url.
    :param task_uid: results will be stored in MongoDB using this value as identifier.
    """
    logger.info(f"[*] Starting bulk verify task for {task_uid}...")

    # Connect to Redis.
    try:
        redis_pool = redis.ConnectionPool.from_url(redis_connection_url)
        redis_instance = redis.Redis(connection_pool=redis_pool, socket_connect_timeout=120)
    except Exception as err:
        logger.error("Bulk Verify: Could not connect to Redis instance.")
        raise self.retry(exc=err)

    # Connect to MongoDB.
    try:
        mongo_client = MongoClient(mongo_host, mongo_port)
        mongo_db = mongo_client["findverify_db"]
        bulk_verify_tasks_collection: Collection = mongo_db["bulk_verify_tasks"]
    except Exception as err:
        logger.error("Bulk Verify: Could not connect to MongoDB instance.")
        raise self.retry(exc=err)

    try:
        results: List[Tuple[str, Dict | None]] = []
        with concurrent.futures.ThreadPoolExecutor(max_workers=100) as executor:
            futures = {
                executor.submit(utils.verify_email, email_id, from_emails_list, redis_instance, "bulk"):
                    email_id for email_id in email_list
            }
            for future in concurrent.futures.as_completed(futures):
                results.append(future.result())

                # Update results in MongoDB as we get them
                bulk_verify_tasks_collection.update_one(
                    {"task_uid": task_uid},
                    {"$set": {"results": results, "status": "in-progress"}},
                )

            # Update status to completed
            bulk_verify_tasks_collection.update_one(
                {"task_uid": task_uid},
                {"$set": {"results": results, "status": "completed"}},
            )

        logger.info(f"[*] Bulk verify task for {task_uid} successful!")

        if webhook_url:
            send_webhook(webhook_url, {"results": results, "task_uid": task_uid})
            logger.info(f"Results sent to webhook url {webhook_url}")

    except Exception as err:
        # Mark this task as failed.
        logger.critical(err, exc_info=True)
        bulk_verify_tasks_collection.update_one(
            {"task_uid": task_uid},
            {"$set": {"status": "failed"}},
        )


@retry(tries=5, backoff=2)
def send_webhook(url: str, data: Dict):
    wh_res = requests.post(
        url,
        json=data
    )
    if wh_res.status_code != 200:
        raise Exception(f"Webhook failed with status code {wh_res.status_code}")


# noinspection PyIncorrectDocstring
@celery.task(bind=True, default_retry_delay=60, max_retries=5, retry_backoff=True)
def bulk_find_task(self, userdetails_list: List[List[str]], from_emails_list: List[str],
                   redis_connection_url: str, task_uid: str):
    """
    Bulk verifies given list of email addresses.

    :param userdetails_list: List of userdetails [["fn", "ln", "domain"], ...].
    :param from_emails_list: List of email ids for use as FROM address during email verification.
    :param redis_connection_url: Redis connection url.
    :param task_uid: results will be stored in MongoDB using this value as identifier.
    """
    logger.info(f"[*] Starting bulk find task for {task_uid}...")

    # Connect to Redis.
    try:
        redis_pool = redis.ConnectionPool.from_url(redis_connection_url)
        redis_instance = redis.Redis(connection_pool=redis_pool, socket_connect_timeout=120)
    except Exception as err:
        logger.error("Bulk Find: Could not connect to Redis instance.")
        raise self.retry(exc=err)

    # Connect to MongoDB.
    try:
        mongo_client = MongoClient(mongo_host, mongo_port)
        mongo_db = mongo_client["findverify_db"]
        bulk_find_tasks_collection: Collection = mongo_db["bulk_find_tasks"]
    except Exception as err:
        logger.error("Bulk Find: Could not connect to MongoDB instance.")
        raise self.retry(exc=err)

    try:
        results: List[Tuple[str, Dict | None]] = []
        with concurrent.futures.ThreadPoolExecutor(max_workers=100) as executor:
            futures = {
                executor.submit(utils.find_email,
                                userdetails[0],
                                userdetails[1],
                                userdetails[2],
                                from_emails_list,
                                redis_instance,
                                "bulk"): userdetails for userdetails in userdetails_list
            }
            for future in concurrent.futures.as_completed(futures):
                results.append(future.result())

                # Update results in MongoDB as we get them
                bulk_find_tasks_collection.update_one(
                    {"task_uid": task_uid},
                    {"$set": {"results": results, "status": "in-progress"}},
                )

            # Update status to completed
            bulk_find_tasks_collection.update_one(
                {"task_uid": task_uid},
                {"$set": {"results": results, "status": "completed"}},
            )

        logger.info(f"[*] Bulk find task for {task_uid} successful!")

    except Exception as err:
        # Mark this task as failed.
        logger.critical(err, exc_info=True)
        bulk_find_tasks_collection.update_one(
            {"task_uid": task_uid},
            {"$set": {"status": "failed"}},
        )
