"""
Utility functions for email verification, MongoDB operations, and greylist retry logic
"""

import os
import re
import time
import logging
import datetime
from typing import Dict, <PERSON>, Tuple, Literal

from redis import Redis
from unidecode import unidecode
from pymongo import MongoClient
from MailChecker import <PERSON><PERSON>he<PERSON>

from verifier import MailVerifier

logger = logging.getLogger(__name__)


def get_mongo_connection():
    """Get MongoDB connection"""
    mongo_host = os.environ['FINDVERIFY_MONGO_HOST']
    mongo_port = int(os.environ['FINDVERIFY_MONGO_PORT'])
    return MongoClient(mongo_host, mongo_port)


def get_mongo_collections():
    """Get MongoDB collections for email verification"""
    client = get_mongo_connection()
    db = client["findverify_db"]
    return {
        'single_verify_tasks': db["single_verify_tasks"],
        'single_find_tasks': db["single_find_tasks"],
        'bulk_verify_tasks': db["bulk_verify_tasks"],
        'bulk_find_tasks': db["bulk_find_tasks"]
    }


def save_single_find_or_verify_result(result: Dict, task_name: str) -> str:
    """
    Save single find or verify result to MongoDB
    :param result: Verification result dictionary
    :param task_name: Name of the task (verify or find)
    :return: Document ID
    """
    try:
        collections = get_mongo_collections()

        document = {
            'status': "completed",
            'created_on': datetime.datetime.now(datetime.timezone.utc),
            'result': result,
        }

        result_doc = collections[f'single_{task_name}_tasks'].insert_one(document)
        return str(result_doc.inserted_id)

    except Exception as e:
        logger.error(f"Error saving single verification: {e}")
        return None


def retry_greylist_domain_with_delay(result: Dict,
                                     verifier_emails: List[str],
                                     redis_instance: Redis,
                                     task_name: str) -> Dict:
    """
    Retry greylist domain with delay
    :param result: Verification result dictionary
    :param verifier_emails: List of email ids to use as FROM address during verification.
    :param redis_instance: Redis instance for storing greylisted emails.
    :param task_name: Name of the task (verify or find)
    :return: Verification result dictionary
    """
    delays = [600, 900, 1800]  # 10, 15 and 30 minutes
    for delay in delays:
        time.sleep(delay)

        if task_name == "verify":
            _, result = verify_email(result["address"][0], verifier_emails, redis_instance)

        elif task_name == "find":
            _, result = find_email(result["user_details"][0],
                                   result["user_details"][1],
                                   result["user_details"][2],
                                   verifier_emails,
                                   redis_instance)

        if not result["greylist"]:
            logger.info(f"[*] Greylist resolved, retry successful!")
            # Delete the greylist record from Redis
            redis_instance.delete(result["address"][1])
            return result

    logger.info(f"[*] Greylist could not be resolved, retry failed!")
    return result


def verify_email(email_id: str,
                 from_emails_list: List[str],
                 redis_instance: Redis,
                 task_from: Literal["bulk", "single"] = "single") -> Tuple[str, Dict | None]:
    """
    Verifies given email and returns a Tuple of email + result dictionary. In case of bad email, returns None in place
    of result dictionary.

    :param email_id: Email address (ex. <EMAIL>)
    :param from_emails_list: List of email addresses to use as FROM address during verification.
    :param redis_instance: Redis instance for storing greylisted emails.
    :param task_from: "bulk" or "single"
    """
    try:
        # Remove unicode
        email_id = unidecode(email_id).lstrip().rstrip()

        # Check if it is in valid format
        if not MailChecker.is_valid_email_format(email_id):
            return email_id, None

        # Initialize MailVerifier class and obtain the smtp_instance
        try:
            mail_verifier = MailVerifier(from_email_list=from_emails_list, redis_instance=redis_instance)
            result = mail_verifier.verify_mail(email_id)

            # If greylist is detected for bulk verify task, retry with delay
            if result["greylist"] and task_from == "bulk":
                logger.info(f"[*] Greylist detected, retrying...")
                result = retry_greylist_domain_with_delay(result, from_emails_list, redis_instance, "verify")

            return email_id, result

        except Exception as err:
            logger.critical(f"Mail Verifier Error (Bulk Verify): {err}", exc_info=True)
            return email_id, None

    # Main exception catcher - so that entire task doesn't fail because of one error.
    except Exception as err:
        logger.critical(err, exc_info=True)
        return email_id, None


def find_email(fname: str, lname: str, domain: str,
               from_emails_list: List[str], redis_instance: Redis,
               task_from: Literal["bulk", "single"] = "single") -> Tuple[List[str], Dict | None]:
    """
    Finds email from given first name, last name and domain. Returns a Tuple of [fn, ln, domain] + result dictionary.
    In case of bad details, returns None in place of user details list.

    :param fname: First name (ex. John)
    :param lname: Last name (ex. Doe)
    :param domain: Domain (ex. example.com)
    :param from_emails_list: List of email addresses to use as FROM address during verification.
    :param redis_instance: Redis instance for storing greylisted emails.
    :param task_from: "bulk" or "single"
    """
    # Convert domain to required format.
    domain: str = re.sub(r"^(www.|https?://www.|https?://)", "", domain, re.IGNORECASE).rstrip('/')

    # Check if all values are available.
    # We need both or one of firstname & lastname, and domain, to find emails.
    if not ((fname or lname) and domain):
        return [fname, lname, domain], None

    # Run find email.
    try:
        mail_verifier = MailVerifier(from_email_list=from_emails_list, redis_instance=redis_instance)
        result = mail_verifier.find_mail(fname, lname, domain)
    except Exception as err:
        logger.critical(err, exc_info=True)
        return [fname, lname, domain], None

    # If greylist is detected for bulk find task, retry with delay
    if result["greylist"] and task_from == "bulk":
        logger.info(f"[*] Greylist detected, retrying...")
        result = retry_greylist_domain_with_delay(result, from_emails_list, redis_instance, "find")

    return [fname, lname, domain], result
